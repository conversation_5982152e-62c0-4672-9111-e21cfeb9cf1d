import React, { useState } from "react";
import InteractiveButton from "./InteractiveButton/InteractiveButton";

interface ViewListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  listing?: {
    id: number;
    user_id: number;
    type: string;
    amount: string;
    rate: string;
    total_value: string;
    payment_method: string;
    description?: string;
    bank_details?: string | null;
    status: string;
    created_at: string;
    user_first_name: string;
    user_last_name: string;
    user_email: string;
  };
  onBuyRequest?: (listingId: string, amount: number) => void;
}

const ViewListingModal: React.FC<ViewListingModalProps> = ({
  isOpen,
  onClose,
  listing,
  onBuyRequest,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  if (!isOpen) return null;

  const handleFileUpload = (file: File) => {
    setUploadedFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleBuyRequest = () => {
    if (listing && onBuyRequest) {
      // Extract numeric amount from the amount string
      const numericAmount = parseFloat(listing.amount);
      onBuyRequest(listing.id.toString(), numericAmount);
    }
  };
  console.log("listing", listing);

  // Parse bank details from JSON string
  const parsedBankDetails = listing?.bank_details
    ? (() => {
        try {
          return JSON.parse(listing.bank_details);
        } catch (e) {
          console.error("Error parsing bank details:", e);
          return null;
        }
      })()
    : null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[520px] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            View Listing (Full Purchase Required)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl font-normal"
          >
            ×
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* 1. Seller & Offer Overview */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">👤</span>
              <h3 className="text-base font-semibold text-gray-900">
                1. Seller & Offer Overview
              </h3>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  Seller Name / Alias
                </span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing
                    ? `${listing.user_first_name} ${listing.user_last_name}`
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Seller Email</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.user_email || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Listing Type</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.type || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Amount (EBA$)</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.amount || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Rate</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.rate || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">
                  Total Amount to Pay
                </span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.total_value || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Payment Method(s)</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.payment_method || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.status || "N/A"}
                </span>
              </div>
            </div>
          </div>

          {/* 2. Seller Notes & Account Details */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">📝</span>
              <h3 className="text-base font-semibold text-gray-900">
                2. Seller Notes & Account Details
              </h3>
            </div>

            <div className="bg-gray-50 p-3 rounded-md mb-4">
              <p className="text-sm text-gray-700 italic">
                {listing?.description || "No specific notes from the seller."}
              </p>
            </div>

            {parsedBankDetails ? (
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-900">
                  Seller Bank Account Details
                </h4>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Bank Name</span>
                  <span className="text-sm text-gray-900 font-medium">
                    {parsedBankDetails.bank_name || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Account Holder</span>
                  <span className="text-sm text-gray-900 font-medium">
                    {parsedBankDetails.account_holder_name || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Account Number</span>
                  <span className="text-sm text-gray-900 font-medium">
                    {parsedBankDetails.account_number || "N/A"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Routing Code</span>
                  <span className="text-sm text-gray-900 font-medium">
                    {parsedBankDetails.routing_code || "N/A"}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <h4 className="text-sm font-semibold text-gray-900">
                  Payment Method Information
                </h4>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> Bank account details are not
                    available for this listing. Please contact the seller
                    directly for payment instructions.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* 3. Purchase Summary */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">📊</span>
              <h3 className="text-base font-semibold text-gray-900">
                3. Purchase Summary (Read-Only)
              </h3>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">EBA$ Amount</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.amount || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Rate</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.rate || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Value</span>
                <span className="text-sm text-gray-900 font-medium">
                  {listing?.total_value || "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Platform Fee</span>
                <span className="text-sm text-gray-900 font-medium">$5.00</span>
              </div>
            </div>
          </div>

          {/* 4. Upload Payment Proof */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#0F2C59] text-lg">📎</span>
              <h3 className="text-base font-semibold text-gray-900">
                4. Upload Payment Proof
              </h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload proof (screenshot or receipt)
              </label>

              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center ${
                  isDragOver ? "border-[#0F2C59] bg-blue-50" : "border-gray-300"
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <div className="text-gray-400 text-4xl mb-2">📎</div>
                <p className="text-sm text-gray-600 mb-2">
                  Click to browse or drag and drop files
                </p>
                <input
                  type="file"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept="image/*,.pdf"
                />
                <label
                  htmlFor="file-upload"
                  className="text-[#0F2C59] hover:underline cursor-pointer text-sm"
                >
                  Browse files
                </label>
              </div>
            </div>

            {/* Image Preview Section */}
            {uploadedFile && (
              <div className="mt-4">
                <div className="flex items-center space-x-2 mb-3">
                  <span className="text-[#16A34A] text-lg">🖼️</span>
                  <h4 className="text-sm font-semibold text-gray-900">
                    Uploaded File Preview
                  </h4>
                </div>

                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {uploadedFile.name}
                    </span>
                    <button
                      onClick={() => setUploadedFile(null)}
                      className="text-red-500 hover:text-red-700 text-sm"
                    >
                      Remove
                    </button>
                  </div>

                  <div className="text-xs text-gray-500 mb-3">
                    Size: {(uploadedFile.size / 1024).toFixed(1)} KB
                  </div>

                  {uploadedFile.type.startsWith("image/") ? (
                    <div className="relative">
                      <img
                        src={URL.createObjectURL(uploadedFile)}
                        alt="Payment proof preview"
                        className="w-full h-48 object-contain border border-gray-300 rounded-md"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-48 border border-gray-300 rounded-md bg-white">
                      <div className="text-center">
                        <div className="text-gray-400 text-4xl mb-2">📄</div>
                        <p className="text-sm text-gray-600">
                          PDF file uploaded
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {uploadedFile.name}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 5. After Buyer Marks as Paid */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="text-[#16A34A] text-lg">⭕</span>
              <h3 className="text-base font-semibold text-gray-900">
                5. After Buyer Marks as Paid
              </h3>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Seller gets notified.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Seller confirms receipt.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Platform transfers {listing?.amount || "N/A"} EBA$ to the
                  buyer's wallet.
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#16A34A] rounded-full"></div>
                <span className="text-sm text-gray-700">
                  Both parties can rate the transaction.
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <InteractiveButton
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </InteractiveButton>

          <div className="flex items-center space-x-3">
            <InteractiveButton className="px-4 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-[#0F2C59]/90 flex items-center space-x-2">
              <span>💰</span>
              <span>Pay Platform Fee ($5)</span>
            </InteractiveButton>

            <InteractiveButton
              onClick={handleBuyRequest}
              className="px-4 py-2 bg-[#16A34A] text-white rounded-md hover:bg-[#16A34A]/90 flex items-center space-x-2"
            >
              <span>✅</span>
              <span>I Have Paid</span>
            </InteractiveButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewListingModal;
